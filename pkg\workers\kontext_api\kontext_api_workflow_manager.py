"""
Remote Kontext API 工作流管理器

负责协调远程 ComfyUI.com API 的调用流程，包括：
- 认证管理
- 图片上传
- 任务提交
- 结果获取
- 错误处理
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from .types import RemoteTaskResult, WorkflowConfig
from .kontext_api_auth_handler import APIAuthHandler
from .kontext_api_upload_manager import ImageUploadManager
from .kontext_api_queue_monitor import RemoteQueueMonitor
from .kontext_api_retry_handler import NetworkRetryHandler
from pkg.core.workflow.manager_base import BaseWorkflowManager, WorkflowResult


class KontextAPIManager(BaseWorkflowManager):
    """Remote Kontext API 工作流管理器"""

    def __init__(self, api_key: str, auth_token: str):
        self.logger = logging.getLogger(__name__)
        self.auth_handler = APIAuthHandler(api_key, auth_token)
        self.upload_manager = ImageUploadManager(self.auth_handler)
        self.queue_monitor = RemoteQueueMonitor(self.auth_handler)
        self.retry_handler = NetworkRetryHandler()

        # 初始化API工作流配置
        from ..kontext.kontext_workflow_models import KontextWorkflowConfig
        self.kontext_api_workflows = {
            1: KontextWorkflowConfig(
                workflow_file="kontext_api_1image.json",
                image_input_count=1,
                description="单图Kontext API编辑"
            ),
            2: KontextWorkflowConfig(
                workflow_file="kontext_api_2images.json",
                image_input_count=2,
                description="双图Kontext API编辑"
            ),
            3: KontextWorkflowConfig(
                workflow_file="kontext_api_3images.json",
                image_input_count=3,
                description="三图Kontext API编辑"
            )
        }



    async def generate_image(self, user_text: str, params: Dict[str, Any], images: Optional[List[bytes]] = None) -> WorkflowResult:
        # 远程API通常不直接用user_text和params拼装workflow_data，这里假设有外部适配
        workflow_data = params.get('workflow_data', {}) if isinstance(params, dict) else {}
        result = await self.submit_workflow(workflow_data, images)
        return result

    async def execute_workflow(self, prompt: str, query: Any) -> WorkflowResult:
        """实现BaseWorkflowManager协议的execute_workflow方法"""
        try:
            # 这里应该根据prompt和query构建workflow_data
            # 暂时使用空的workflow_data作为占位符
            workflow_data = {}
            images = getattr(query, 'images', None) if query else None
            return await self.submit_workflow(workflow_data, images)
        except Exception as e:
            return WorkflowResult(
                success=False,
                images=[],
                metadata={},
                workflow_type="kontext_api",
                error_message=str(e)
            )

    async def get_generation_info(self, task_id: str) -> WorkflowResult:
        """实现BaseWorkflowManager协议的get_generation_info方法"""
        try:
            status_info = await self.get_task_status(task_id)
            return WorkflowResult(
                success=status_info.get('status') == 'completed',
                images=[],
                metadata=status_info,
                workflow_type="kontext_api",
                error_message=status_info.get('error_message')
            )
        except Exception as e:
            return WorkflowResult(
                success=False,
                images=[],
                metadata={},
                workflow_type="kontext_api",
                error_message=str(e)
            )

    async def submit_workflow(self, workflow_data: Dict[str, Any], images: Optional[List[bytes]] = None, timeout: int = 300) -> WorkflowResult:
        remote_result = await self._submit_workflow_and_adapt(workflow_data, images, timeout)
        return remote_result

    async def _submit_workflow_and_adapt(self, workflow_data: Dict[str, Any], images: Optional[List[bytes]], timeout: int) -> WorkflowResult:
        try:
            remote_task: RemoteTaskResult = await self.submit_workflow(workflow_data, images, timeout)
            # 远程API返回的images为List[str]（URL），此处暂不下载，直接填充metadata
            return WorkflowResult(
                success=remote_task.status == "completed",
                images=[],  # 如需本地图片数据可后续扩展下载
                metadata={"remote_images": remote_task.images, **remote_task.metadata},
                workflow_type="kontext_api",
                error_message=remote_task.error_message
            )
        except Exception as e:
            return WorkflowResult(
                success=False,
                images=[],
                metadata={},
                workflow_type="kontext_api",
                error_message=str(e)
            )

    async def _submit_workflow_with_retry(self, workflow_data: Dict[str, Any]) -> str:
        """带重试机制的工作流提交"""
        
        async def submit():
            return await self.auth_handler.submit_workflow(workflow_data)
        
        return await self.retry_handler.execute_with_retry(submit)
    
    def _update_workflow_images(self, workflow_data: Dict[str, Any], uploaded_images: List[str]) -> Dict[str, Any]:
        """更新工作流中的图片引用"""
        # 这里需要根据具体的工作流格式来更新图片引用
        # 暂时返回原始数据，具体实现需要根据 ComfyUI 工作流格式
        return workflow_data
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        return await self.queue_monitor.get_task_status(task_id)
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        return await self.queue_monitor.cancel_task(task_id)

    def select_workflow(self, image_count: int):
        """根据图片数量选择合适的API工作流"""

        if image_count <= 0:
            raise ValueError("Kontext API工作流至少需要一张图片")

        # 限制最大图片数量为3
        effective_count = min(image_count, 3)

        config = self.kontext_api_workflows[effective_count]
        self.logger.info(f"选择Kontext API工作流: {config.description} (输入图片: {effective_count}张)")

        # 如果用户提供了超过3张图片，记录警告
        if image_count > 3:
            self.logger.warning(f"用户提供了{image_count}张图片，但Kontext API工作流最多支持3张图片，将使用前3张图片")

        return config
    
    async def get_api_quota(self) -> Dict[str, Any]:
        """获取API配额信息"""
        return await self.auth_handler.get_quota_info()
    
    async def close(self) -> None:
        """关闭管理器，清理资源"""
        await self.upload_manager.close()
        await self.queue_monitor.close()
        await self.auth_handler.close() 