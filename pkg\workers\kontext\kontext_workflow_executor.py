"""
Kontext 工作流执行模块
负责Kontext相关的工作流执行、API调用、错误处理等
"""
from typing import Dict, Any, List, Optional
import random
import logging
from .kontext_base_executor import BaseKontextExecutor
from .local_executor import LocalKontextExecutor
from .api_executor import ApiKontextExecutor
from pkg.core.workflow.manager_base import WorkflowResult


class KontextWorkflowExecutor:
    """
    Kontext工作流调度与执行
    """
    def __init__(self, mode: str = 'local', api_url: Optional[str] = None, timeout: int = 180, api_key: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        executor: BaseKontextExecutor
        if mode == 'local':
            # 本地模式使用本地ComfyUI
            local_api_url = api_url or "http://localhost:8188"
            executor = LocalKontextExecutor(api_url=local_api_url, timeout=timeout)
        else:
            # API模式也使用本地ComfyUI，通过API节点调用远程服务
            remote_api_url = api_url or "http://127.0.0.1:8188"
            executor = ApiKontextExecutor(api_url=remote_api_url, timeout=timeout, api_key=api_key)
        self.executor = executor
        self.mode = mode

    async def prepare_workflow_data(self, config: Dict[str, Any], images: List[bytes], prompt: str, aspect_ratio: str, generation_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        组装最终的工作流数据
        """
        # 生成随机种子
        seed = random.randint(100000000000000, 999999999999999)

        # 组装参数
        params = {
            'prompt': prompt,
            'aspect_ratio': aspect_ratio,
            'guidance': generation_params.get('guidance', 4.0),
            'steps': generation_params.get('steps', 60),
            'seed': seed,
            'prompt_upsampling': generation_params.get('prompt_upsampling', False),
            'images': images
        }

        # 添加负面提示词（如果有）
        if 'negative_prompt' in generation_params:
            params['negative_prompt'] = generation_params['negative_prompt']

        return params

    def get_last_workflow_data(self) -> Optional[Dict[str, Any]]:
        """
        获取最后执行的ComfyUI工作流数据
        🔥 新增：用于--again功能获取真正的工作流数据
        """
        self.logger.info(f"🔍 [WORKFLOW-EXECUTOR-DEBUG] get_last_workflow_data 被调用")
        self.logger.info(f"🔍 [WORKFLOW-EXECUTOR-DEBUG] executor类型: {type(self.executor)}")
        self.logger.info(f"🔍 [WORKFLOW-EXECUTOR-DEBUG] 是否有current_workflow_data: {hasattr(self.executor, 'current_workflow_data')}")

        if hasattr(self.executor, 'current_workflow_data'):
            current_data = self.executor.current_workflow_data
            self.logger.info(f"🔍 [WORKFLOW-EXECUTOR-DEBUG] current_workflow_data值: {current_data is not None}")
            if current_data:
                self.logger.info(f"🔍 [WORKFLOW-EXECUTOR-DEBUG] current_workflow_data类型: {type(current_data)}")
                return current_data

        self.logger.warning(f"🔍 [WORKFLOW-EXECUTOR-DEBUG] 未找到current_workflow_data")
        return None

    async def execute_workflow(self, workflow_file: str, params: Dict[str, Any], *args, **kwargs) -> WorkflowResult:
        """
        调用底层executor执行工作流
        """
        try:
            self.logger.info(f"🔍 [DEBUG] KontextWorkflowExecutor.execute_workflow 开始")
            self.logger.info(f"🔍 [DEBUG] - 工作流文件: {workflow_file}")
            self.logger.info(f"🔍 [DEBUG] - 执行器类型: {type(self.executor).__name__}")
            self.logger.info(f"🔍 [DEBUG] - 模式: {self.mode}")
            self.logger.info(f"🔍 [DEBUG] - 参数数量: {len(params)}")

            # 🔥 即将调用底层执行器
            self.logger.info(f"🔍 [DEBUG] 即将调用 self.executor.execute_workflow")

            import time
            start_time = time.time()
            result = await self.executor.execute_workflow(workflow_file, params, *args, **kwargs)
            end_time = time.time()

            self.logger.info(f"🔍 [DEBUG] 底层执行器返回，耗时: {end_time - start_time:.2f}秒")
            self.logger.info(f"🔍 [DEBUG] - 结果类型: {type(result)}")
            self.logger.info(f"🔍 [DEBUG] - 成功: {getattr(result, 'success', 'N/A')}")

            return result if result is not None else WorkflowResult(success=False, error_message='执行器返回空结果')
        except Exception as e:
            self.logger.error(f"🔍 [DEBUG] KontextWorkflowExecutor 异常: {e}")
            import traceback
            self.logger.error(f"🔍 [DEBUG] 异常堆栈: {traceback.format_exc()}")
            return WorkflowResult(success=False, error_message=f'执行工作流失败: {str(e)}')
    
    async def close(self):
        """关闭执行器"""
        if hasattr(self.executor, 'close'):
            await self.executor.close()

kontext_workflow_executor = KontextWorkflowExecutor() 